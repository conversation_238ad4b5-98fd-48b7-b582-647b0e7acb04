# CHANGELOG


## v0.2.0 (2025-05-25)

### Documentation

- Update documentation for Job Search AI Assistant
  ([`22e43cb`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/22e43cbee6829360bf269785555306155cfbea9e))

### Features

- Add test configuration and fixtures, implement tests for application and health check
  ([`a6ca11f`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/a6ca11fa04c8ddd1a2d19b1da8be118ad10318d9))


## v0.1.0 (2025-05-25)

### Chores

- Update dependencies and configuration files.
  ([`0635622`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/063562227addce79a188124a6b2ee5a0cab4e6fa))

### Features

- Add FastAPI integration with health check and API setup
  ([`4bc6357`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/4bc6357f7fb38165b18fe88f68a1e09bcf74288d))

Introduced FastAPI application with health check endpoint and modular API structure. Included
  configurations, schema, and middleware for error handling, CORS, and gzip compression. Updated the
  main entry point to run the API using Uvicorn.


## v0.0.0 (2025-05-24)

### Chores

- Setup project structure and workflow
  ([`7294b18`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/7294b182102bf88bfc2de53caae89144f8598213))

### Documentation

- Updated README.md and other documentation.
  ([`0877120`](https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant/commit/087712008fd1dc6f367596693e9ba3abf01c507b))
