# Codecov configuration file
# See: https://docs.codecov.com/docs/codecov-yaml

coverage:
  # Precision of coverage reporting
  precision: 2
  round: nearest

  # Coverage status settings
  status:
    project:
      default:
        # Don't fail CI if coverage decreases slightly
        informational: true
        # Optional: Set target coverage threshold
        target: auto
        threshold: 1%
    patch:
      default:
        informational: true
        target: auto
        threshold: 1%

# Ignore patterns
ignore:
  - "tests/*"
  - "setup.py"
  - "config.py"

# Comment settings on PRs
comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: false  # if true: only post if coverage changes
  require_base: false    # [true :: must have a base report to post]
  require_head: true     # [true :: must have a head report to post]

# GitHub Checks settings
github_checks:
  annotations: true

# Validation
validate: true
