.md-typeset .admonition,
.md-typeset details {
  border-width: 0;
  border-left-width: 4px;
}

/* :root {
  --black: #222022;
  --grey: #353038;
  --white: #ffffff;
  --nebula: #d7ff64;
  --flare: #6340ac;
  --rock: #78876e;
  --galaxy: #261230;
  --space: #30173d;
  --comet: #6f5d6f;
  --cosmic: #de5fe9;
  --sun: #ffac2f;
  --electron: #46ebe1;
  --aurora: #46eb74;
  --constellation: #5f6de9;
  --neutron: #cff3cf;
  --proton: #f6afbc;
  --nebula: #cdcbfb;
  --supernova: #f1aff6;
  --starlight: #f4f4f1;
  --lunar: #fbf2fc;
  --asteroid: #e3cee3;
  --crater: #f0dfdf;
}

[data-md-color-scheme="astral-light"] {
  --md-default-bg-color--dark: var(--black);
  --md-primary-fg-color: var(--galaxy);
  --md-typeset-a-color: var(--flare);
  --md-accent-fg-color: var(--cosmic);
}

[data-md-color-scheme="astral-dark"] {
  --md-default-bg-color: var(--black);
  --md-default-fg-color: var(--white);
  --md-default-fg-color--light: var(--white);
  --md-default-fg-color--lighter: var(--white);
  --md-primary-fg-color: var(--space);
  --md-primary-bg-color: var(--white);
  --md-accent-fg-color: var(--cosmic);

  --md-typeset-color: var(--white);
  --md-typeset-a-color: var(--nebula);
  --md-typeset-mark-color: var(--sun);

  --md-code-fg-color: var(--white);
  --md-code-bg-color: var(--grey);

  --md-code-hl-comment-color: var(--asteroid);
  --md-code-hl-punctuation-color: var(--asteroid);
  --md-code-hl-generic-color: var(--supernova);
  --md-code-hl-variable-color: var(--starlight);
  --md-code-hl-string-color: var(--nebula);
  --md-code-hl-keyword-color: var(--supernova);
  --md-code-hl-operator-color: var(--supernova);
  --md-code-hl-number-color: var(--electron);
  --md-code-hl-special-color: var(--electron);
  --md-code-hl-function-color: var(--neutron);
  --md-code-hl-constant-color: var(--nebula);
  --md-code-hl-name-color: var(--md-code-fg-color);

  --md-typeset-del-color: hsla(6, 90%, 60%, 0.15);
  --md-typeset-ins-color: hsla(150, 90%, 44%, 0.15);

  --md-typeset-table-color: hsla(0, 0%, 100%, 0.12);
  --md-typeset-table-color--light: hsla(0, 0%, 100%, 0.035);
} */
