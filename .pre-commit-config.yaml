repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: "v5.0.0"
    hooks:
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-toml
      - id: check-yaml
        exclude: "mkdocs.yml"
      - id: check-json
        exclude: ^.devcontainer/devcontainer.json
      - id: pretty-format-json
        exclude: ^.devcontainer/devcontainer.json
        args: [--autofix, --no-sort-keys]
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: name-tests-test
        args: ['--pytest-test-first']

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: "v0.11.11"
    hooks:
      - id: ruff
        name: Ruff linter
        args: [--exit-non-zero-on-fix]
      - id: ruff-format
        name: Ruff formatter

  - repo: local
    hooks:
      - id: validate-commit-message
        name: Validate Commit Message
        description: Validate commit message format for semantic versioning.
        entry: .hooks/commit-msg
        language: script
        stages: [commit-msg]
