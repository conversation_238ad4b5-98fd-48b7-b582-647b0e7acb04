site_name: job-search-ai-assistant
repo_url: https://github.com/RY<PERSON><PERSON>IEV-SERHII/job-search-ai-assistant
site_url: https://RYZHAIEV-SERHII.github.io/job-search-ai-assistant
site_description: A comprehensive job search aggregator built with Python 3.13 and FastAPI that integrates multiple job platforms to streamline the job hunting process.
site_author: R<PERSON><PERSON><PERSON>IEV-SERHII
edit_uri: edit/main/docs/
repo_name: RY<PERSON><PERSON>IEV-SERHII/job-search-ai-assistant
copyright: Maintained by <a href="https://RYZHAIEV-SERHII.com">RYZHAIEV-SERHII</a>.

nav:
  - Home: index.md
  - Modules: modules.md
  - Blog: blog/index.md

# Configuration
theme:
  name: material
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - content.code.select
    # - content.footnote.tooltips
    # - content.tabs.link
    - content.tooltips
    # - header.autohide
    # - navigation.expand
    - navigation.footer
    - navigation.indexes
    # - navigation.instant
    # - navigation.instant.prefetch
    # - navigation.instant.progress
    # - navigation.prune
    - navigation.sections
    - navigation.tabs
    # - navigation.tabs.sticky
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    # - toc.integrate
  palette:
    # Default dark theme
    - scheme: slate
      primary: black
      accent: black
      toggle:
        icon: material/brightness-7
        name: Switch to light mode
    # Light mode
    - scheme: default
      primary: black
      accent: black
      toggle:
        icon: material/brightness-4
        name: Switch to dark mode
  font:
    text: Roboto
    code: Roboto Mono
  icon:
    repo: fontawesome/brands/github
    logo: material/medical-bag

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - tables
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: shaneholloman
      repo: uvi
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - pymdownx.tasklist:
      custom_checkbox: true
      clickable_checkbox: true
  - pymdownx.tilde

# Plugins
plugins:
  - gh-admonitions
  - blog
  - search:
      separator: '[\s\u200b\-_,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  - mkdocstrings:
      handlers:
        python:
          options:
            show_source: true
          paths: [src]
validation:
  omitted_files: warn
  absolute_links: warn
  unrecognized_links: warn
  anchors: warn

extra_css:
  - stylesheets/extra.css

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/job-search-ai-assistant
